// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2880C76C2E2621D900BB8C27 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2880C76B2E2621D900BB8C27 /* StoreKit.framework */; };
		2880C7992E2DF4BA00BB8C27 /* Vision.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2880C7982E2DF4BA00BB8C27 /* Vision.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2818DFEA2DFA9CA5005903F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2818DFD42DFA9CA3005903F7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2818DFDB2DFA9CA3005903F7;
			remoteInfo = NIENIE;
		};
		2818DFF42DFA9CA5005903F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2818DFD42DFA9CA3005903F7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2818DFDB2DFA9CA3005903F7;
			remoteInfo = NIENIE;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2818DFDC2DFA9CA3005903F7 /* NIENIE.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NIENIE.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIENIETests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIENIEUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2880C76B2E2621D900BB8C27 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		2880C7982E2DF4BA00BB8C27 /* Vision.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Vision.framework; path = System/Library/Frameworks/Vision.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		28E5FF292E028F0800A13A56 /* Exceptions for "NIENIE" folder in "NIENIE" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2818DFDE2DFA9CA3005903F7 /* NIENIE */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				28E5FF292E028F0800A13A56 /* Exceptions for "NIENIE" folder in "NIENIE" target */,
			);
			path = NIENIE;
			sourceTree = "<group>";
		};
		2818DFEC2DFA9CA5005903F7 /* NIENIETests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIENIETests;
			sourceTree = "<group>";
		};
		2818DFF62DFA9CA5005903F7 /* NIENIEUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIENIEUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2818DFD92DFA9CA3005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2880C7992E2DF4BA00BB8C27 /* Vision.framework in Frameworks */,
				2880C76C2E2621D900BB8C27 /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFE62DFA9CA5005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFF02DFA9CA5005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2818DFD32DFA9CA3005903F7 = {
			isa = PBXGroup;
			children = (
				2818DFDE2DFA9CA3005903F7 /* NIENIE */,
				2818DFEC2DFA9CA5005903F7 /* NIENIETests */,
				2818DFF62DFA9CA5005903F7 /* NIENIEUITests */,
				2880C76A2E2621D900BB8C27 /* Frameworks */,
				2818DFDD2DFA9CA3005903F7 /* Products */,
			);
			sourceTree = "<group>";
		};
		2818DFDD2DFA9CA3005903F7 /* Products */ = {
			isa = PBXGroup;
			children = (
				2818DFDC2DFA9CA3005903F7 /* NIENIE.app */,
				2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */,
				2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2880C76A2E2621D900BB8C27 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2880C7982E2DF4BA00BB8C27 /* Vision.framework */,
				2880C76B2E2621D900BB8C27 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2818DFDB2DFA9CA3005903F7 /* NIENIE */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818DFFD2DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIE" */;
			buildPhases = (
				2818DFD82DFA9CA3005903F7 /* Sources */,
				2818DFD92DFA9CA3005903F7 /* Frameworks */,
				2818DFDA2DFA9CA3005903F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2818DFDE2DFA9CA3005903F7 /* NIENIE */,
			);
			name = NIENIE;
			productName = NIENIE;
			productReference = 2818DFDC2DFA9CA3005903F7 /* NIENIE.app */;
			productType = "com.apple.product-type.application";
		};
		2818DFE82DFA9CA5005903F7 /* NIENIETests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818E0002DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIETests" */;
			buildPhases = (
				2818DFE52DFA9CA5005903F7 /* Sources */,
				2818DFE62DFA9CA5005903F7 /* Frameworks */,
				2818DFE72DFA9CA5005903F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2818DFEB2DFA9CA5005903F7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2818DFEC2DFA9CA5005903F7 /* NIENIETests */,
			);
			name = NIENIETests;
			productName = NIENIETests;
			productReference = 2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2818DFF22DFA9CA5005903F7 /* NIENIEUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818E0032DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIEUITests" */;
			buildPhases = (
				2818DFEF2DFA9CA5005903F7 /* Sources */,
				2818DFF02DFA9CA5005903F7 /* Frameworks */,
				2818DFF12DFA9CA5005903F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2818DFF52DFA9CA5005903F7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2818DFF62DFA9CA5005903F7 /* NIENIEUITests */,
			);
			name = NIENIEUITests;
			productName = NIENIEUITests;
			productReference = 2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2818DFD42DFA9CA3005903F7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					2818DFDB2DFA9CA3005903F7 = {
						CreatedOnToolsVersion = 16.4;
					};
					2818DFE82DFA9CA5005903F7 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2818DFDB2DFA9CA3005903F7;
					};
					2818DFF22DFA9CA5005903F7 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2818DFDB2DFA9CA3005903F7;
					};
				};
			};
			buildConfigurationList = 2818DFD72DFA9CA3005903F7 /* Build configuration list for PBXProject "NIENIE" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 2818DFD32DFA9CA3005903F7;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2818DFDD2DFA9CA3005903F7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2818DFDB2DFA9CA3005903F7 /* NIENIE */,
				2818DFE82DFA9CA5005903F7 /* NIENIETests */,
				2818DFF22DFA9CA5005903F7 /* NIENIEUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2818DFDA2DFA9CA3005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFE72DFA9CA5005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFF12DFA9CA5005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2818DFD82DFA9CA3005903F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFE52DFA9CA5005903F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFEF2DFA9CA5005903F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2818DFEB2DFA9CA5005903F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
			targetProxy = 2818DFEA2DFA9CA5005903F7 /* PBXContainerItemProxy */;
		};
		2818DFF52DFA9CA5005903F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
			targetProxy = 2818DFF42DFA9CA5005903F7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2818DFFB2DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2818DFFC2DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2818DFFE2DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIENIE/NIENIE.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIENIE/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机，以便拍摄照片进行处理。例如：拍摄人物照片进行背景替换。如果禁止，您将无法使用拍照上传功能。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要保存处理后的结果或者您感兴趣的图片到您的相册。如果禁止，您将无法保存。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册，以便选择照片进行处理。例如：选取人物照片进行背景替换。如果禁止，您将无法从相册选择照片上传。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIE;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2818DFFF2DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIENIE/NIENIE.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIENIE/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机，以便拍摄照片进行处理。例如：拍摄人物照片进行背景替换。如果禁止，您将无法使用拍照上传功能。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要保存处理后的结果或者您感兴趣的图片到您的相册。如果禁止，您将无法保存。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册，以便选择照片进行处理。例如：选取人物照片进行背景替换。如果禁止，您将无法从相册选择照片上传。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIE;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2818E0012DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIETests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIENIE.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIENIE";
			};
			name = Debug;
		};
		2818E0022DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIETests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIENIE.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIENIE";
			};
			name = Release;
		};
		2818E0042DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIEUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIENIE;
			};
			name = Debug;
		};
		2818E0052DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIEUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIENIE;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2818DFD72DFA9CA3005903F7 /* Build configuration list for PBXProject "NIENIE" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818DFFB2DFA9CA5005903F7 /* Debug */,
				2818DFFC2DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818DFFD2DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIE" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818DFFE2DFA9CA5005903F7 /* Debug */,
				2818DFFF2DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818E0002DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIETests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818E0012DFA9CA5005903F7 /* Debug */,
				2818E0022DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818E0032DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIEUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818E0042DFA9CA5005903F7 /* Debug */,
				2818E0052DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2818DFD42DFA9CA3005903F7 /* Project object */;
}
