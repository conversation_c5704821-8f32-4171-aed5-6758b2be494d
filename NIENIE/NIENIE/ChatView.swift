import SwiftUI

// 消息模型
struct Message: Identifiable, Codable, Equatable {
    let message_id: Int
    let conversation_id: String
    let sender_id: Int
    let receiver_id: Int
    let content: String
    let status: Int
    let created_at: Date
    var is_self: Bool

    var id: Int { message_id }

    static func == (lhs: Message, rhs: Message) -> Bool {
        return lhs.message_id == rhs.message_id
    }
}

// 聊天视图的ViewModel
class ChatViewModel: ObservableObject {
    @Published var messages: [Message] = [] // 所有消息的本地存储
    @Published var displayedMessages: [Message] = [] // 当前显示的消息
    @Published var inputText = ""
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var canLoadMoreHistory = false // 是否还能加载更多历史消息
    @Published var isLoadingHistory = false // 是否正在加载历史消息
    @Published var shouldScrollToBottom = false // 是否应该滚动到底部
    @Published var targetMessageId: Int? = nil // 要定位到的目标消息ID

    private let conversationId: String
    private let userId: Int
    private let otherUserId: Int
    private var isRequestInProgress = false
    private let messagesPerPage = 30 // 每页显示的消息数量
    
    init(conversationId: String, userId: Int, otherUserId: Int) {
        self.conversationId = conversationId
        self.userId = userId
        self.otherUserId = otherUserId
        loadMessagesFromCache()
        // 先加载新消息，确保获取到最新的消息后再显示UI
        loadMessages()
    }
    
    // 加载用户信息
    func loadUserInfo(userId: Int, completion: @escaping (String) -> Void) {
        guard let url = URL(string: "https://sorealhuman.com:8888/users/\(userId)/info") else {
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool, success,
                   let userData = json["user"] as? [String: Any],
                   let avatar = userData["avatar"] as? String {
                    
                    DispatchQueue.main.async {
                        completion(avatar)
                    }
                }
            } catch {
                print("解析用户信息失败: \(error)")
            }
        }.resume()
    }
    
    func loadMessages() {
        // 防止重复请求
        if isRequestInProgress {
            return
        }

        isLoading = true
        isRequestInProgress = true
        errorMessage = nil

        // 获取当前会话的最后一条消息ID
        let lastMessageId = LastMessageIDManager.shared.getLastMessageID(for: conversationId)

        guard let url = URL(string: "https://sorealhuman.com:8888/messages/conversation/\(conversationId)/user/\(userId)?last_message_id=\(lastMessageId)") else {
            isLoading = false
            isRequestInProgress = false
            errorMessage = "无效的URL"
            return
        }
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.isRequestInProgress = false
                
                if let error = error {
                    self?.errorMessage = "加载失败: \(error.localizedDescription)"
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    self?.errorMessage = "无效的响应"
                    return
                }
                
                if httpResponse.statusCode == 404 {
                    // 确保消息列表为空
                    self?.messages = []
                    self?.saveMessagesToCache()
                    return
                }
                
                if httpResponse.statusCode != 200 {
                    self?.errorMessage = "服务器错误: \(httpResponse.statusCode)"
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "没有数据"
                    return
                }
                
                do {
                    // 先尝试解析为JSON，检查返回格式
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        // 检查是否有success字段
                        if let success = json["success"] as? Bool, success {
                            // 检查是否有data字段
                            if let responseData = json["data"] as? [String: Any] {
                                // 尝试获取messages数组
                                if let messagesArray = responseData["messages"] as? [[String: Any]] {
                                    // 有消息数据，解析它们
                                    var parsedMessages: [Message] = []

                                    for messageData in messagesArray {
                                        if let messageId = messageData["message_id"] as? Int,
                                           let senderId = messageData["sender_id"] as? Int,
                                           let receiverId = messageData["receiver_id"] as? Int {

                                            // content 可能是字符串或数字，需要转换为字符串
                                            let content: String
                                            if let contentString = messageData["content"] as? String {
                                                content = contentString
                                            } else if let contentNumber = messageData["content"] as? Int {
                                                content = String(contentNumber)
                                            } else if let contentNumber = messageData["content"] as? Double {
                                                content = String(contentNumber)
                                            } else {
                                                continue
                                            }

                                            // status 字段是可选的，如果没有则默认为1
                                            let status = messageData["status"] as? Int ?? 1


                                            // 处理时间字符串
                                            var createdAt = Date()
                                            if let timeString = messageData["created_at"] as? String {
                                                let formatter = ISO8601DateFormatter()
                                                createdAt = formatter.date(from: timeString) ?? Date()
                                            }

                                            let conversationId = messageData["conversation_id"] as? String ?? self?.conversationId ?? ""
                                            let isSelf = messageData["is_self"] as? Bool ?? (senderId == self?.userId)

                                            let message = Message(
                                                message_id: messageId,
                                                conversation_id: conversationId,
                                                sender_id: senderId,
                                                receiver_id: receiverId,
                                                content: content,
                                                status: status,
                                                created_at: createdAt,
                                                is_self: isSelf
                                            )

                                            parsedMessages.append(message)
                                        } else {
                                            print("消息解析失败，缺少必要字段: \(messageData)")
                                        }
                                    }

                                    // 将新消息添加到现有消息列表中
                                    self?.addNewMessages(parsedMessages)
                                    return
                                } else if let emptyArray = responseData["messages"] as? [Any], emptyArray.isEmpty {
                                    // 空数组，没有新消息
                                    self?.updateDisplayedMessages(shouldScroll: false)
                                    return
                                }
                            }
                        } else {
                            // 请求不成功
                            if let message = json["message"] as? String {
                                self?.errorMessage = message
                            } else {
                                self?.errorMessage = "获取消息失败"
                            }
                            return
                        }
                    }
                    
                    // 如果上面的解析失败，尝试使用标准解码器
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    
                    let response = try decoder.decode(APIResponse<[Message]>.self, from: data)
                    if response.success {
                        let messagesData = response.data ?? []
                        let processedMessages = messagesData.map { message in
                            var msg = message
                            msg.is_self = message.sender_id == self?.userId
                            return msg
                        }
                        self?.addNewMessages(processedMessages)
                    } else {
                        self?.errorMessage = response.message ?? "未知错误"
                    }
                } catch {
                    // 如果解析失败，确保更新显示列表
                    self?.updateDisplayedMessages()
                }
            }
        }.resume()
    }

    /// 添加新消息到现有消息列表中
    private func addNewMessages(_ newMessages: [Message]) {

        guard !newMessages.isEmpty else {
            // 即使没有新消息，也要确保显示列表是最新的
            updateDisplayedMessages()
            return
        }

        // 过滤掉已存在的消息（避免重复）
        let existingMessageIds = Set(messages.map { $0.message_id })
        let uniqueNewMessages = newMessages.filter { !existingMessageIds.contains($0.message_id) }


        if !uniqueNewMessages.isEmpty {
            // 添加新消息到列表末尾
            messages.append(contentsOf: uniqueNewMessages)

            // 按时间排序确保消息顺序正确
            messages.sort { $0.created_at < $1.created_at }

            // 更新最后消息ID
            let _ = LastMessageIDManager.shared.getLastMessageID(for: conversationId)
            LastMessageIDManager.shared.updateLastMessageIDFromMessages(uniqueNewMessages, for: conversationId)
            let _ = LastMessageIDManager.shared.getLastMessageID(for: conversationId)

            // 更新显示的消息列表
            updateDisplayedMessages()

            // 保存到缓存
            saveMessagesToCache()

        } else {
            // 没有新的唯一消息，但仍需要更新显示列表
            updateDisplayedMessages()
        }
    }

    /// 更新显示的消息列表（显示最近的消息）
    private func updateDisplayedMessages(shouldScroll: Bool = true) {
        let sortedMessages = messages.sorted { $0.created_at < $1.created_at }

        // 如果有目标消息，确保它在显示列表中
        if let targetId = targetMessageId,
           let targetIndex = sortedMessages.firstIndex(where: { $0.message_id == targetId }) {

            // 计算要显示的消息范围，确保目标消息在中间位置
            let halfPage = messagesPerPage / 2
            let startIndex = max(0, targetIndex - halfPage)
            let endIndex = min(sortedMessages.count, startIndex + messagesPerPage)

            displayedMessages = Array(sortedMessages[startIndex..<endIndex])
            canLoadMoreHistory = startIndex > 0
            shouldScrollToBottom = false // 不滚动到底部，而是定位到目标消息
        } else {
            // 正常显示最近的消息
            displayedMessages = Array(sortedMessages.suffix(messagesPerPage))
            canLoadMoreHistory = sortedMessages.count > messagesPerPage
            shouldScrollToBottom = shouldScroll
        }
    }

    /// 设置目标消息ID
    func setTargetMessage(_ messageId: Int) {
        targetMessageId = messageId
        updateDisplayedMessages(shouldScroll: false)
    }

    /// 加载更多历史消息到显示列表
    func loadMoreHistoryMessages() {
        guard canLoadMoreHistory && !isLoadingHistory else { return }

        isLoadingHistory = true

        let sortedMessages = messages.sorted { $0.created_at < $1.created_at }
        let currentDisplayCount = displayedMessages.count
        let totalCount = sortedMessages.count

        // 检查是否还有更多历史消息可以显示
        if currentDisplayCount < totalCount {
            // 计算要加载的新消息数量
            let additionalCount = min(messagesPerPage, totalCount - currentDisplayCount)
            let startIndex = totalCount - currentDisplayCount - additionalCount
            let endIndex = totalCount - currentDisplayCount

            if startIndex >= 0 {
                let newMessages = Array(sortedMessages[startIndex..<endIndex])

                // 将新消息添加到显示列表的开头，不触发滚动
                displayedMessages = newMessages + displayedMessages
                shouldScrollToBottom = false // 加载历史消息时不滚动

                // 更新是否还能加载更多的状态
                canLoadMoreHistory = startIndex > 0

            }
        } else {
            canLoadMoreHistory = false
        }

        isLoadingHistory = false
    }

    func loadMessagesFromCache() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")

            // 检查文件是否存在
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                messages = try decoder.decode([Message].self, from: data)

                // 更新最后消息ID
                if !messages.isEmpty {
                    LastMessageIDManager.shared.updateLastMessageIDFromMessages(messages, for: conversationId)
                }

                // 更新显示的消息列表（从缓存加载时不滚动）
                updateDisplayedMessages(shouldScroll: false)
            }
        } catch {
            print("读取缓存的消息失败: \(error)")
            // 不显示错误，因为这只是缓存读取失败
        }
    }
    
    func saveMessagesToCache() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
            
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(messages)
            try data.write(to: fileURL)
        } catch {
            print("保存消息到缓存失败: \(error)")
        }
    }
    
    func sendMessage() {
        let content = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        if content.isEmpty {
            return
        }
        
        // 清空输入框
        inputText = ""
        
        guard let url = URL(string: "https://sorealhuman.com:8888/messages/send") else {
            errorMessage = "无效的URL"
            return
        }
        
        let parameters: [String: Any] = [
            "sender_id": userId,
            "receiver_id": otherUserId,
            "content": content
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            errorMessage = "参数编码失败"
            return
        }
        
        // 添加临时消息到UI
        let tempMessage = Message(
            message_id: -1, // 临时ID
            conversation_id: conversationId,
            sender_id: userId,
            receiver_id: otherUserId,
            content: content,
            status: 1,
            created_at: Date(),
            is_self: true
        )
        
        // 添加到UI
        messages.append(tempMessage)
        updateDisplayedMessages()
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "发送失败: \(error.localizedDescription)"
                    // 移除临时消息
                    self?.messages.removeAll { $0.message_id == -1 }
                    self?.updateDisplayedMessages()
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "没有数据"
                    // 移除临时消息
                    self?.messages.removeAll { $0.message_id == -1 }
                    self?.updateDisplayedMessages()
                    return
                }
                
                do {
                    // 先尝试解析为JSON
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        if let success = json["success"] as? Bool, success {
                            if let responseData = json["data"] as? [String: Any],
                               let messageId = responseData["message_id"] as? Int,
                               let conversationId = responseData["conversation_id"] as? String {
                                
                                // 创建正式消息
                                var createdAt = Date()
                                if let timeString = responseData["created_at"] as? String {
                                    let formatter = ISO8601DateFormatter()
                                    createdAt = formatter.date(from: timeString) ?? Date()
                                }
                                
                                let newMessage = Message(
                                    message_id: messageId,
                                    conversation_id: conversationId,
                                    sender_id: self?.userId ?? 0,
                                    receiver_id: self?.otherUserId ?? 0,
                                    content: content,
                                    status: 1,
                                    created_at: createdAt,
                                    is_self: true
                                )
                                
                                // 替换临时消息
                                if let index = self?.messages.firstIndex(where: { $0.message_id == -1 }) {
                                    self?.messages[index] = newMessage
                                } else {
                                    self?.messages.append(newMessage)
                                }

                                // 更新最后消息ID
                                LastMessageIDManager.shared.updateLastMessageID(newMessage.message_id, for: self?.conversationId ?? "")

                                // 发送通知给会话列表页面，通知有新消息（用户主动发送）
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("NewMessageReceived"),
                                    object: nil,
                                    userInfo: [
                                        "conversation_id": conversationId,
                                        "message_id": messageId,
                                        "sender_id": self?.userId ?? 0,
                                        "receiver_id": self?.otherUserId ?? 0,
                                        "content": content,
                                        "status": 1,  
                                        "created_at": createdAt.ISO8601Format(),
                                        "is_self": true
                                    ]
                                )

                                // 更新显示的消息列表
                                self?.updateDisplayedMessages()

                                self?.saveMessagesToCache()
                                return
                            }
                        } else {
                            // 请求不成功
                            if let message = json["message"] as? String {
                                self?.errorMessage = message
                            } else {
                                self?.errorMessage = "发送消息失败"
                            }
                            // 移除临时消息
                            self?.messages.removeAll { $0.message_id == -1 }
                            self?.updateDisplayedMessages()
                            return
                        }
                    }
                    
                    // 如果上面的解析失败，尝试使用标准解码器
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    
                    let response = try decoder.decode(APIResponse<MessageResponse>.self, from: data)
                    if response.success, let responseData = response.data {
                        // 添加新消息到列表
                        let newMessage = Message(
                            message_id: responseData.message_id,
                            conversation_id: responseData.conversation_id,
                            sender_id: self?.userId ?? 0,
                            receiver_id: self?.otherUserId ?? 0,
                            content: content,
                            status: 1,
                            created_at: responseData.created_at,
                            is_self: true
                        )
                        
                        // 替换临时消息
                        if let index = self?.messages.firstIndex(where: { $0.message_id == -1 }) {
                            self?.messages[index] = newMessage
                        } else {
                            self?.messages.append(newMessage)
                        }

                        // 更新最后消息ID
                        LastMessageIDManager.shared.updateLastMessageID(newMessage.message_id, for: self?.conversationId ?? "")

                        // 更新显示的消息列表
                        self?.updateDisplayedMessages()

                        self?.saveMessagesToCache()
                    } else {
                        self?.errorMessage = response.message ?? "未知错误"
                        // 移除临时消息
                        self?.messages.removeAll { $0.message_id == -1 }
                        self?.updateDisplayedMessages()
                    }
                } catch {
                    print("解析失败: \(error)")
                    self?.errorMessage = "解析失败"
                    // 移除临时消息
                    self?.messages.removeAll { $0.message_id == -1 }
                    self?.updateDisplayedMessages()
                }
            }
        }.resume()
    }
    
    func deleteMessage(_ message: Message) {
        // 只在本地删除消息，不调用后端接口
        // 注意：删除操作不会影响最后消息ID的值
        messages.removeAll { $0.message_id == message.message_id }

        // 更新显示的消息列表
        updateDisplayedMessages()

        saveMessagesToCache()
    }

    func clearChatHistory() {
        // 保存最后一条消息ID，不能删除
        let _ = LastMessageIDManager.shared.getLastMessageID(for: conversationId)

        // 只清空本地消息，不调用后端接口
        messages = []

        // 更新显示的消息列表
        updateDisplayedMessages()

        saveMessagesToCache()

        // 注意：不清除最后消息ID，保持会话的连续性

        // 发送通知，让会话列表页面刷新
        NotificationCenter.default.post(
            name: NSNotification.Name("ConversationHistoryCleared"),
            object: nil,
            userInfo: ["conversation_id": conversationId]
        )
    }

    func clearAllMessages() {
        // 清空内存中的消息数据（用于会话被删除时）
        messages = []
        displayedMessages = []

        // 不清除任何ID记录，保留最后消息ID和已读消息ID
        // 这样可以确保：
        // 1. 未读消息计算的连续性
        // 2. 从用户头像进入聊天时不会获取全部历史记录

    }

    func handleNewMessage(_ messageData: [String: Any]) {
        // 从WebSocket消息数据创建Message对象
        guard let messageId = messageData["message_id"] as? Int,
              let conversationId = messageData["conversation_id"] as? String,
              let senderId = messageData["sender_id"] as? Int,
              let receiverId = messageData["receiver_id"] as? Int,
              let content = messageData["content"] as? String,
              let status = messageData["status"] as? Int,
              let createdAtString = messageData["created_at"] as? String,
              let isSelf = messageData["is_self"] as? Bool else {
            return
        }

        // 解析时间
        let formatter = ISO8601DateFormatter()
        let createdAt = formatter.date(from: createdAtString) ?? Date()

        // 创建新消息
        let newMessage = Message(
            message_id: messageId,
            conversation_id: conversationId,
            sender_id: senderId,
            receiver_id: receiverId,
            content: content,
            status: status,
            created_at: createdAt,
            is_self: isSelf
        )

        // 检查消息是否已存在（避免重复）
        if !messages.contains(where: { $0.message_id == messageId }) {
            messages.append(newMessage)

            // 更新最后消息ID
            LastMessageIDManager.shared.updateLastMessageID(messageId, for: conversationId)

            // 更新显示的消息列表
            updateDisplayedMessages()

            saveMessagesToCache()
        }
    }
}

struct ChatView: View {
    @StateObject private var viewModel: ChatViewModel
    @State private var showingDeleteAlert = false
    @State private var messageToDelete: Message?
    @State private var keyboardHeight: CGFloat = 0
    @State private var showSettingsSheet = false
    @State private var textHeight: CGFloat = 36 // 初始文本框高度
    @State private var dragOffset: CGFloat = 0 // 右滑手势的偏移量
    @State private var showUserProfile = false // 显示用户主页的状态
    @Environment(\.dismiss) var dismiss

    let conversationId: String
    let otherUserId: Int
    let otherUsername: String
    let userId: Int
    let targetMessage: Message? // 要定位到的目标消息
    @State private var otherUserAvatar: String = ""

    init(conversationId: String, otherUserId: Int, otherUsername: String, userId: Int, otherUserAvatar: String = "", targetMessage: Message? = nil) {
        self.conversationId = conversationId
        self.otherUserId = otherUserId
        self.otherUsername = otherUsername
        self.userId = userId
        self.targetMessage = targetMessage
        self._otherUserAvatar = State(initialValue: otherUserAvatar)
        _viewModel = StateObject(wrappedValue: ChatViewModel(conversationId: conversationId, userId: userId, otherUserId: otherUserId))
    }
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    // 点击非键盘区域关闭键盘
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }

            VStack(spacing: 0) {
                // 聊天内容区域
                if viewModel.isLoading && viewModel.messages.isEmpty {
                    Spacer()
                    ProgressView()
                    Spacer()
                } else if let errorMessage = viewModel.errorMessage, viewModel.messages.isEmpty {
                    Spacer()
                    VStack {
                        Text(errorMessage)
                            .foregroundColor(.gray)
                        
                        Button("重试") {
                            viewModel.loadMessages()
                        }
                        .padding()
                        .foregroundColor(.white)
                        .background(Color.blue)
                        .cornerRadius(8)
                        .padding(.top)
                    }
                    Spacer()
                } else {
                    ScrollViewReader { scrollView in
                        ScrollView {
                            LazyVStack(spacing: 10) {
                                // 下拉加载更多历史消息的指示器
                                if viewModel.canLoadMoreHistory {
                                    HStack {
                                        Spacer()
                                        if viewModel.isLoadingHistory {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                            Text("加载中...")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                        } else {
                                            Text("下拉加载更多历史消息")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                        }
                                        Spacer()
                                    }
                                    .padding(.vertical, 8)
                                    .onAppear {
                                        // 当这个视图出现时，自动加载更多历史消息
                                        if !viewModel.isLoadingHistory {
                                            viewModel.loadMoreHistoryMessages()
                                        }
                                    }
                                }

                                ForEach(viewModel.displayedMessages) { message in
                                    MessageBubble(
                                        message: message,
                                        otherUserAvatar: otherUserAvatar,
                                        currentUserAvatar: UserState.shared.avatar,
                                        isHighlighted: viewModel.targetMessageId == message.message_id,
                                        onAvatarTap: {
                                            presentUserProfile()
                                        }
                                    )
                                    .id(message.id)
                                    .onLongPressGesture {
                                        messageToDelete = message
                                        showingDeleteAlert = true
                                    }
                                }
                            }
                            .padding(.horizontal)
                            .padding(.top, 10)
                        }
                        .refreshable {
                            // 下拉刷新时加载更多历史消息
                            if viewModel.canLoadMoreHistory {
                                viewModel.loadMoreHistoryMessages()
                            }
                        }
                        .onTapGesture {
                            // 点击聊天区域关闭键盘
                            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                        }
                        .onChange(of: viewModel.displayedMessages) { oldMessages, newMessages in
                            // 如果有目标消息，滚动到目标消息
                            if let targetId = viewModel.targetMessageId,
                               let targetMessage = newMessages.first(where: { $0.message_id == targetId }) {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    withAnimation {
                                        scrollView.scrollTo(targetMessage.id, anchor: .center)
                                    }
                                    // 延时清除高亮效果
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                        viewModel.targetMessageId = nil
                                    }
                                }
                            } else if viewModel.shouldScrollToBottom, let lastMessage = newMessages.last {
                                // 只有在应该滚动到底部时才滚动
                                withAnimation {
                                    scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                                }
                            }
                        }
                        .onAppear {
                            // 如果有目标消息，滚动到目标消息
                            if let targetId = viewModel.targetMessageId,
                               let targetMessage = viewModel.displayedMessages.first(where: { $0.message_id == targetId }) {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    withAnimation {
                                        scrollView.scrollTo(targetMessage.id, anchor: .center)
                                    }
                                    // 延时清除高亮效果
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                        viewModel.targetMessageId = nil
                                    }
                                }
                            } else if let lastMessage = viewModel.displayedMessages.last {
                                scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                // 底部输入框
                VStack(spacing: 0) {
                    Divider()

                    HStack(alignment: .bottom, spacing: 8) {
                        // 多行文本输入框
                        ZStack(alignment: .topLeading) {
                            // 占位符
                            if viewModel.inputText.isEmpty {
                                Text("发送消息")
                                    .foregroundColor(.gray.opacity(0.8))
                                    .padding(.leading, 12)
                                    .padding(.top, 12)
                                    .allowsHitTesting(false)
                            }

                            // 使用自定义TextEditor
                            ChatTextEditor(
                                text: $viewModel.inputText,
                                onSubmit: {
                                    viewModel.sendMessage()
                                },
                                onTextChange: {
                                    updateTextHeight()
                                }
                            )
                            .frame(minHeight: textHeight, maxHeight: min(textHeight, 108)) // 最大3行高度
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(UIColor.systemGray6))
                            .cornerRadius(16)
                        }

                        // 发送按钮
                        Button(action: {
                            // 发送消息
                            viewModel.sendMessage()
                        }) {
                            Text("发送")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .white)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? Color.gray.opacity(0.3) : Color.blue)
                                .cornerRadius(16)
                        }
                        .disabled(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                        .simultaneousGesture(
                            LongPressGesture(minimumDuration: 0.5)
                                .onEnded { _ in
                                    // 长按换行
                                    viewModel.inputText += "\n"
                                    updateTextHeight()
                                }
                        )
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                }
            }
            .offset(x: dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只允许向右滑动
                        if value.translation.width > 0 {
                            dragOffset = value.translation.width
                        }
                    }
                    .onEnded { value in
                        // 如果滑动距离超过50点，则返回上一页
                        if value.translation.width > 50 {
                            dismiss()
                        } else {
                            // 否则回弹到原位置
                            withAnimation(.spring()) {
                                dragOffset = 0
                            }
                        }
                    }
            )
            .navigationTitle(otherUsername)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: HStack(spacing: 8) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                    }
                    
                    // 显示对方头像
                    if !otherUserAvatar.isEmpty {
                        Button(action: {
                            presentUserProfile()
                        }) {
                            if otherUserAvatar.contains("http") {
                                // 远程头像
                                AsyncImage(url: URL(string: otherUserAvatar)) { phase in
                                    if let image = phase.image {
                                        image
                                            .resizable()
                                            .scaledToFill()
                                    } else {
                                        Circle()
                                            .fill(Color.gray.opacity(0.3))
                                    }
                                }
                                .frame(width: 30, height: 30)
                                .clipShape(Circle())
                            } else {
                                // 本地头像
                                Image(otherUserAvatar)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 30, height: 30)
                                    .clipShape(Circle())
                            }
                        }
                    }
                },
                trailing: Button(action: {
                    showSettingsSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.black)
                        .padding(.horizontal)
                }
            )
            .alert(isPresented: $showingDeleteAlert) {
                Alert(
                    title: Text("删除消息"),
                    message: Text("确定要删除这条消息吗？"),
                    primaryButton: .destructive(Text("删除")) {
                        if let message = messageToDelete {
                            viewModel.deleteMessage(message)
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .sheet(isPresented: $showSettingsSheet) {
                ChatSettingsView(
                    conversationId: conversationId,
                    userId: userId,
                    otherUserId: otherUserId,
                    otherUsername: otherUsername,
                    otherUserAvatar: otherUserAvatar,
                    currentUserAvatar: UserState.shared.avatar,
                    onClearHistory: {
                        viewModel.clearChatHistory()
                        showSettingsSheet = false
                    }
                )
            }
        }
        .onAppear {
            // 设置当前页面状态并通过WebSocket更新
            UserState.shared.updatePageStatus(page: "ChatView", conversationId: conversationId)

            // 如果有目标消息，设置目标消息ID
            if let targetMessage = targetMessage {
                viewModel.setTargetMessage(targetMessage.message_id)
            }

            // 强制刷新消息，确保获取到最新的消息
            viewModel.loadMessages()

            // 注册通知观察者，监听新消息
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("NewMessageReceived"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let receivedConversationId = userInfo["conversation_id"] as? String,
                   receivedConversationId == conversationId {
                    // 收到当前会话的新消息，直接添加到UI而不重新加载
                    // 将 [AnyHashable: Any] 转换为 [String: Any]
                    let messageData = userInfo.compactMapValues { $0 }.reduce(into: [String: Any]()) { result, element in
                        if let key = element.key as? String {
                            result[key] = element.value
                        }
                    }
                    viewModel.handleNewMessage(messageData)
                }
            }

            // 注册通知观察者，监听从后台返回的刷新请求
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("RefreshChatAfterBackground"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let receivedConversationId = userInfo["conversation_id"] as? String,
                   receivedConversationId == conversationId {
                    // 重新加载消息以获取可能错过的新消息
                    viewModel.loadMessages()
                }
            }

            // 注册通知观察者，监听会话缓存清除
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("ConversationCacheCleared"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let clearedConversationId = userInfo["conversation_id"] as? String,
                   clearedConversationId == conversationId {
                    // 清空当前的消息数据
                    viewModel.clearAllMessages()
                }
            }

            // 如果没有头像信息，尝试从API获取
            if otherUserAvatar.isEmpty {
                viewModel.loadUserInfo(userId: otherUserId) { avatar in
                    if !avatar.isEmpty {
                        otherUserAvatar = avatar
                    }
                }
            }
        }
        .onDisappear {
            UserState.shared.updatePageStatus(page: "ConversationListView", conversationId: "")

            // 更新已读消息ID为当前会话的最后一条消息ID
            let lastMessageId = LastMessageIDManager.shared.getLastMessageID(for: conversationId)
            if lastMessageId > 0 {
                LastMessageIDManager.shared.setReadMessageID(lastMessageId, for: conversationId)
            }

            // 通知会话列表页面刷新当前会话状态（清零未读数）
            NotificationCenter.default.post(
                name: NSNotification.Name("ChatViewDidDisappear"),
                object: nil,
                userInfo: ["conversation_id": conversationId]
            )

            // 移除通知观察者
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("NewMessageReceived"), object: nil)
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RefreshChatAfterBackground"), object: nil)
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ConversationCacheCleared"), object: nil)
        }
    }

    // 更新文本框高度
    private func updateTextHeight() {
        let lineHeight: CGFloat = 20
        let padding: CGFloat = 16
        let lines = max(1, min(3, viewModel.inputText.components(separatedBy: .newlines).count))
        textHeight = CGFloat(lines) * lineHeight + padding
    }

    // 使用UIKit手动弹出用户个人资料页
    private func presentUserProfile() {
        let userProfileView = UserProfileView(
            profileUserId: otherUserId,
            profileUsername: otherUsername,
            profileAvatar: otherUserAvatar
        ).environmentObject(UserState.shared)

        let hostingController = UIHostingController(rootView: userProfileView)
        hostingController.modalPresentationStyle = .fullScreen

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            var topController = rootViewController
            while let presentedViewController = topController.presentedViewController {
                topController = presentedViewController
            }
            topController.present(hostingController, animated: true, completion: nil)
        }
    }
}

// 消息气泡视图
struct MessageBubble: View {
    let message: Message
    let otherUserAvatar: String
    let currentUserAvatar: String
    let isHighlighted: Bool
    let onAvatarTap: () -> Void

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.is_self {
                Spacer()

                // 自己的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .clipShape(BubbleShape(isFromCurrentUser: true))
                    .padding(.trailing, 8)

                // 自己的头像（右侧）
                Image(currentUserAvatar)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
            } else {
                // 对方的头像（左侧）
                Button(action: onAvatarTap) {
                    if !otherUserAvatar.isEmpty {
                        if otherUserAvatar.contains("http") {
                            // 远程头像
                            AsyncImage(url: URL(string: otherUserAvatar)) { phase in
                                if let image = phase.image {
                                    image
                                        .resizable()
                                        .scaledToFill()
                                } else {
                                    Circle()
                                        .fill(Color.gray.opacity(0.3))
                                }
                            }
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                        } else {
                            // 本地头像
                            Image(otherUserAvatar)
                                .resizable()
                                .scaledToFill()
                                .frame(width: 40, height: 40)
                                .clipShape(Circle())
                        }
                    } else {
                        // 默认头像
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 40, height: 40)
                    }
                }

                // 对方的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color(UIColor.systemGray6))
                    .foregroundColor(.black)
                    .clipShape(BubbleShape(isFromCurrentUser: false))
                    .padding(.leading, 8)

                Spacer()
            }
        }
        .padding(.horizontal, 4)
        .background(
            // 高亮背景
            isHighlighted ? Color.yellow.opacity(0.3) : Color.clear
        )
        .cornerRadius(8)
        .animation(.easeInOut(duration: 0.3), value: isHighlighted)
    }
}

// 气泡形状
struct BubbleShape: Shape {
    var isFromCurrentUser: Bool
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, cornerRadius: 16)
        return Path(path.cgPath)
    }
}

// 聊天设置视图
struct ChatSettingsView: View {
    let conversationId: String
    let userId: Int
    let otherUserId: Int
    let otherUsername: String
    let otherUserAvatar: String
    let currentUserAvatar: String
    let onClearHistory: () -> Void
    @State private var showReportSheet = false
    @State private var showSearchView = false
    @State private var isMuted = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            List {
                Section {
                    // 查找聊天记录选项
                    Button(action: {
                        showSearchView = true
                    }) {
                        HStack {
                            Text("查找聊天记录")
                                .foregroundColor(.black)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                    }

                    // 免打扰开关
                    HStack {
                        Text("消息免打扰")
                        Spacer()
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Toggle("", isOn: $isMuted)
                                .onChange(of: isMuted) { oldValue, newValue in
                                    setMuteStatus(newValue)
                                }
                        }
                    }
                }

                Section {
                    Button(action: {
                        showReportSheet = true
                    }) {
                        HStack {
                            Text("举报")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }

                    Button(action: onClearHistory) {
                        HStack {
                            Text("清空聊天记录")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }

                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("聊天设置")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showReportSheet) {
            ChatReportSheet(conversationId: conversationId, userId: userId)
        }
        .fullScreenCover(isPresented: $showSearchView) {
            ChatSearchView(
                conversationId: conversationId,
                currentUserId: userId,
                otherUserId: otherUserId,
                otherUsername: otherUsername,
                otherUserAvatar: otherUserAvatar,
                currentUserAvatar: currentUserAvatar
            )
        }
        .onAppear {
            loadMuteStatus()
        }
    }

    private func loadMuteStatus() {
        // 从本地管理器加载免打扰状态
        isMuted = ConversationMuteManager.shared.getMuteStatus(for: conversationId)
    }

    private func setMuteStatus(_ muted: Bool) {
        isLoading = true
        errorMessage = nil

        guard let url = URL(string: "https://sorealhuman.com:8888/messages/set_conversation_mute") else {
            errorMessage = "无效的URL"
            isLoading = false
            // 恢复开关状态
            isMuted = !muted
            return
        }

        let parameters: [String: Any] = [
            "user_id": userId,
            "conversation_id": conversationId,
            "is_muted": muted
        ]

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            errorMessage = "参数编码失败"
            isLoading = false
            // 恢复开关状态
            isMuted = !muted
            return
        }

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                isLoading = false

                if let error = error {
                    errorMessage = "设置失败: \(error.localizedDescription)"
                    // 恢复开关状态
                    isMuted = !muted
                    return
                }

                guard let data = data else {
                    errorMessage = "没有数据"
                    // 恢复开关状态
                    isMuted = !muted
                    return
                }

                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        if let success = json["success"] as? Bool, success {
                            // 设置成功，更新本地状态
                            ConversationMuteManager.shared.setMuteStatus(muted, for: conversationId)
                        } else {
                            // 设置失败
                            if let message = json["message"] as? String {
                                errorMessage = message
                            } else {
                                errorMessage = "设置免打扰状态失败"
                            }
                            // 恢复开关状态
                            isMuted = !muted
                        }
                    }
                } catch {
                    errorMessage = "解析响应失败"
                    // 恢复开关状态
                    isMuted = !muted
                }
            }
        }.resume()
    }
}

// 自定义聊天文本编辑器
struct ChatTextEditor: UIViewRepresentable {
    @Binding var text: String
    var onSubmit: () -> Void
    var onTextChange: () -> Void

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.backgroundColor = .clear
        textView.delegate = context.coordinator
        textView.isScrollEnabled = true
        textView.returnKeyType = .send
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        if uiView.text != text {
            uiView.text = text
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        var parent: ChatTextEditor

        init(_ parent: ChatTextEditor) {
            self.parent = parent
        }

        func textViewDidChange(_ textView: UITextView) {
            parent.text = textView.text
            parent.onTextChange()
        }

        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // 当用户点击键盘上的发送按钮时，text会是"\n"
            if text == "\n" {
                // 如果输入框不为空，则提交
                if !textView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    parent.onSubmit()
                }
                return false // 不插入换行符
            }
            return true
        }
    }
}

struct ChatView_Previews: PreviewProvider {
    static var previews: some View {
        ChatView(conversationId: "1_2", otherUserId: 2, otherUsername: "测试用户", userId: 1)
    }
}
