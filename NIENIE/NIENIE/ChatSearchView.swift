import SwiftUI

// 搜索结果项模型
struct SearchResultItem: Identifiable {
    let id = UUID()
    let message: Message
    let senderAvatar: String
    let senderName: String
    
    // 格式化时间显示
    var formattedTime: String {
        let formatter = DateFormatter()
        let now = Date()
        let calendar = Calendar.current
        
        // 判断是否为今天
        if calendar.isDate(message.created_at, inSameDayAs: now) {
            // 当天：只显示时分 (HH:mm)
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDate(message.created_at, equalTo: now, toGranularity: .year) {
            // 本年但非今天：显示月日 (MM/dd)
            formatter.dateFormat = "MM/dd"
        } else {
            // 非本年：显示年月日 (yyyy/MM/dd)
            formatter.dateFormat = "yyyy/MM/dd"
        }
        
        return formatter.string(from: message.created_at)
    }
}

// 聊天记录搜索视图模型
class ChatSearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchResults: [SearchResultItem] = []
    @Published var isLoading = false

    private let conversationId: String
    private let currentUserId: Int
    private let otherUserId: Int
    private let otherUsername: String
    private let otherUserAvatar: String
    private let currentUserAvatar: String
    private var searchWorkItem: DispatchWorkItem?
    
    init(conversationId: String, currentUserId: Int, otherUserId: Int, otherUsername: String, otherUserAvatar: String, currentUserAvatar: String) {
        self.conversationId = conversationId
        self.currentUserId = currentUserId
        self.otherUserId = otherUserId
        self.otherUsername = otherUsername
        self.otherUserAvatar = otherUserAvatar
        self.currentUserAvatar = currentUserAvatar
    }

    // 防抖搜索
    func debouncedSearch() {
        // 取消之前的搜索任务
        searchWorkItem?.cancel()

        let trimmedText = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else {
            searchResults = []
            return
        }

        // 创建新的搜索任务
        let workItem = DispatchWorkItem { [weak self] in
            self?.performSearch(for: trimmedText)
        }

        searchWorkItem = workItem

        // 延迟300毫秒执行搜索
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)
    }
    
    // 执行搜索
    private func performSearch(for searchText: String) {
        isLoading = true

        // 从本地缓存中搜索消息
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let messages = self.loadMessagesFromCache()
            let filteredMessages = messages.filter { message in
                message.content.localizedCaseInsensitiveContains(searchText)
            }

            // 转换为搜索结果项
            let results = filteredMessages.map { message in
                let isFromCurrentUser = message.sender_id == self.currentUserId
                let senderAvatar = isFromCurrentUser ? self.currentUserAvatar : self.otherUserAvatar
                let senderName = isFromCurrentUser ? "我" : self.otherUsername

                return SearchResultItem(
                    message: message,
                    senderAvatar: senderAvatar,
                    senderName: senderName
                )
            }

            // 按时间倒序排列（最新的在前）
            let sortedResults = results.sorted { $0.message.created_at > $1.message.created_at }

            DispatchQueue.main.async {
                // 确保搜索文本没有改变
                if self.searchText.trimmingCharacters(in: .whitespacesAndNewlines) == searchText {
                    self.searchResults = sortedResults
                }
                self.isLoading = false
            }
        }
    }

    // 搜索聊天记录（立即搜索，用于提交时）
    func searchMessages() {
        let trimmedText = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else {
            searchResults = []
            return
        }

        // 取消防抖任务，立即执行搜索
        searchWorkItem?.cancel()
        performSearch(for: trimmedText)
    }
    
    // 从本地缓存加载消息
    private func loadMessagesFromCache() -> [Message] {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
            
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                return try decoder.decode([Message].self, from: data)
            }
        } catch {
            print("读取缓存的消息失败: \(error)")
        }
        return []
    }
}

// 聊天记录搜索视图
struct ChatSearchView: View {
    @StateObject private var viewModel: ChatSearchViewModel
    @Environment(\.dismiss) var dismiss
    @FocusState private var isSearchFieldFocused: Bool
    @State private var navigateToChatView = false
    @State private var selectedMessage: Message?

    let conversationId: String
    let currentUserId: Int
    let otherUserId: Int
    let otherUsername: String
    let otherUserAvatar: String
    let currentUserAvatar: String
    
    init(conversationId: String, currentUserId: Int, otherUserId: Int, otherUsername: String, otherUserAvatar: String, currentUserAvatar: String) {
        self.conversationId = conversationId
        self.currentUserId = currentUserId
        self.otherUserId = otherUserId
        self.otherUsername = otherUsername
        self.otherUserAvatar = otherUserAvatar
        self.currentUserAvatar = currentUserAvatar
        
        _viewModel = StateObject(wrappedValue: ChatSearchViewModel(
            conversationId: conversationId,
            currentUserId: currentUserId,
            otherUserId: otherUserId,
            otherUsername: otherUsername,
            otherUserAvatar: otherUserAvatar,
            currentUserAvatar: currentUserAvatar
        ))
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.white.edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        // 点击非键盘区域关闭键盘
                        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                    }
                
                VStack(spacing: 0) {
                    // 搜索栏
                    HStack(spacing: 12) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.gray)
                            
                            TextField("搜索聊天记录", text: $viewModel.searchText)
                                .font(.system(size: 16))
                                .focused($isSearchFieldFocused)
                                .onSubmit {
                                    viewModel.searchMessages()
                                }
                                .onChange(of: viewModel.searchText) { oldValue, newValue in
                                    // 防抖搜索
                                    viewModel.debouncedSearch()
                                }
                        }
                        .padding(.vertical, 10)
                        .padding(.horizontal, 15)
                        .background(Color(UIColor.systemGray6))
                        .cornerRadius(10)
                        
                        // 取消按钮
                        Button("取消") {
                            dismiss()
                        }
                        .foregroundColor(.blue)
                        .font(.system(size: 16))
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)
                    
                    // 搜索结果列表
                    if viewModel.isLoading {
                        Spacer()
                        ProgressView()
                            .scaleEffect(1.2)
                        Spacer()
                    } else if viewModel.searchText.isEmpty {
                        Spacer()
                        VStack(spacing: 8) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 40))
                                .foregroundColor(.gray.opacity(0.6))
                            Text("输入关键词搜索聊天记录")
                                .font(.system(size: 16))
                                .foregroundColor(.gray)
                        }
                        Spacer()
                    } else if viewModel.searchResults.isEmpty {
                        Spacer()
                        VStack(spacing: 8) {
                            Image(systemName: "doc.text.magnifyingglass")
                                .font(.system(size: 40))
                                .foregroundColor(.gray.opacity(0.6))
                            Text("没有找到相关聊天记录")
                                .font(.system(size: 16))
                                .foregroundColor(.gray)
                        }
                        Spacer()
                    } else {
                        List(viewModel.searchResults) { result in
                            SearchResultRow(result: result) {
                                // 点击搜索结果，跳转到聊天页面并定位到该消息
                                selectedMessage = result.message
                                navigateToChatView = true
                            }
                            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.white)
                        }
                        .listStyle(PlainListStyle())
                        .onTapGesture {
                            // 点击列表区域关闭键盘
                            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                        }
                    }
                }
            }
            .navigationTitle("查找聊天记录")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .background(
                NavigationLink(
                    destination: ChatView(
                        conversationId: conversationId,
                        otherUserId: otherUserId,
                        otherUsername: otherUsername,
                        userId: currentUserId,
                        otherUserAvatar: otherUserAvatar,
                        targetMessage: selectedMessage
                    ),
                    isActive: $navigateToChatView
                ) {
                    EmptyView()
                }
                .hidden()
            )
        }
        .onAppear {
            // 自动弹出键盘
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isSearchFieldFocused = true
            }
        }
    }
}

// 搜索结果行视图
struct SearchResultRow: View {
    let result: SearchResultItem
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 发送者头像
                if result.senderAvatar.isEmpty {
                    // 默认头像
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Text(String(result.senderName.prefix(1)))
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(.gray)
                        )
                } else if result.senderAvatar.contains("http") {
                    // 远程头像URL
                    AsyncImage(url: URL(string: result.senderAvatar)) { phase in
                        if let image = phase.image {
                            image
                                .resizable()
                                .scaledToFill()
                        } else {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                        }
                    }
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                } else {
                    // 本地头像名称
                    Image(result.senderAvatar)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())
                }

                // 消息内容和时间
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(result.senderName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.gray)

                        Spacer()

                        Text(result.formattedTime)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                    }

                    Text(result.message.content)
                        .font(.system(size: 16))
                        .foregroundColor(.black)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(.vertical, 4)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ChatSearchView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ChatSearchView(
                conversationId: "1_2",
                currentUserId: 1,
                otherUserId: 2,
                otherUsername: "测试用户",
                otherUserAvatar: "avatar",
                currentUserAvatar: "current_avatar"
            )
        }
    }
}
